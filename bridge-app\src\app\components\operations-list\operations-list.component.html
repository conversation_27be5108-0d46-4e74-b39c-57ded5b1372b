<div class="operations-container">
  <div class="header">
    <h1>Gestion des Opérations de Change</h1>
    <p class="subtitle">Bureau de Change - Déclarations à la Banque Centrale de Tunisie</p>
  </div>

  <!-- Filtres -->
  <div class="filters-section">
    <h3>Filtres</h3>
    <div class="filters-grid">
      <div class="filter-group">
        <label for="mois">Mois:</label>
        <select id="mois" [(ngModel)]="selectedMois" (change)="onFilterChange()" class="form-control">
          <option *ngFor="let mois of moisOptions" [value]="mois.value">{{mois.label}}</option>
        </select>
      </div>

      <div class="filter-group">
        <label for="annee">Année:</label>
        <select id="annee" [(ngModel)]="selectedAnnee" (change)="onFilterChange()" class="form-control">
          <option *ngFor="let annee of anneeOptions" [value]="annee">{{annee}}</option>
        </select>
      </div>

      <div class="filter-group">
        <label for="devise">Devise:</label>
        <select id="devise" [(ngModel)]="selectedDevise" (change)="onFilterChange()" class="form-control">
          <option value="">Toutes les devises</option>
          <option *ngFor="let devise of deviseOptions" [value]="devise">{{devise}}</option>
        </select>
      </div>

      <div class="filter-group">
        <label for="type">Type:</label>
        <select id="type" [(ngModel)]="selectedType" (change)="onFilterChange()" class="form-control">
          <option value="">Tous les types</option>
          <option *ngFor="let type of typeOptions" [value]="type">{{type}}</option>
        </select>
      </div>

      <div class="filter-group">
        <label for="statut">Statut:</label>
        <select id="statut" [(ngModel)]="selectedStatut" (change)="onFilterChange()" class="form-control">
          <option value="">Tous les statuts</option>
          <option *ngFor="let statut of statutOptions" [value]="statut">{{statut}}</option>
        </select>
      </div>
    </div>
  </div>

  <!-- Statistiques -->
  <div class="stats-section">
    <div class="stats-grid">
      <div class="stat-card">
        <h4>Total Opérations</h4>
        <p class="stat-value">{{getTotalOperations()}}</p>
      </div>
      <div class="stat-card">
        <h4>Montant Total (TND)</h4>
        <p class="stat-value">{{getTotalMontantTND() | number:'1.2-2'}} TND</p>
      </div>
      <div class="stat-card">
        <h4>Période</h4>
        <p class="stat-value">{{getMoisLabel(selectedMois)}} {{selectedAnnee}}</p>
      </div>
    </div>
  </div>

  <!-- Boutons d'action -->
  <div class="actions-section">
    <h3>Actions pour {{getMoisLabel(selectedMois)}} {{selectedAnnee}}</h3>
    <div class="action-buttons">
      <button class="btn btn-primary" (click)="genererDeclaration()">
        <i class="icon-file"></i>
        Générer Déclaration BCT
      </button>
      <button class="btn btn-success" (click)="telechargerDeclaration()">
        <i class="icon-download"></i>
        Télécharger Déclaration
      </button>
    </div>
  </div>

  <!-- Tableau des opérations -->
  <div class="operations-section">
    <h3>Liste des Opérations</h3>
    <div class="table-container">
      <table class="operations-table">
        <thead>
          <tr>
            <th>Date</th>
            <th>N° Opération</th>
            <th>Type</th>
            <th>Devise</th>
            <th>Montant Devise</th>
            <th>Taux</th>
            <th>Montant TND</th>
            <th>Client</th>
            <th>CIN</th>
            <th>Statut</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let operation of filteredOperations" [class]="'status-' + operation.statut.toLowerCase()">
            <td>{{operation.date | date:'dd/MM/yyyy'}}</td>
            <td>{{operation.numeroOperation}}</td>
            <td>
              <span class="badge" [class]="'badge-' + operation.type.toLowerCase()">
                {{operation.type}}
              </span>
            </td>
            <td>{{operation.devise}}</td>
            <td>{{operation.montantDevise | number:'1.2-2'}}</td>
            <td>{{operation.tauxChange | number:'1.4-4'}}</td>
            <td>{{operation.montantTND | number:'1.2-2'}} TND</td>
            <td>{{operation.clientNom}}</td>
            <td>{{operation.clientCIN}}</td>
            <td>
              <span class="status-badge" [class]="'status-' + operation.statut.toLowerCase()">
                {{operation.statut}}
              </span>
            </td>
          </tr>
        </tbody>
      </table>

      <div *ngIf="filteredOperations.length === 0" class="no-data">
        <p>Aucune opération trouvée pour les critères sélectionnés.</p>
      </div>
    </div>
  </div>
</div>
