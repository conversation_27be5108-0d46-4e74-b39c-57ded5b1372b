.operations-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

// Messages d'alerte
.alert {
  padding: 15px 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 500;

  &.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }

  &.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }
}

.header {
  text-align: center;
  margin-bottom: 30px;

  h1 {
    color: #2c3e50;
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 600;
  }

  .subtitle {
    color: #7f8c8d;
    font-size: 1.1rem;
    margin: 0;
  }
}

.filters-section {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 10px;
  margin-bottom: 25px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);

  h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.3rem;
  }
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.filter-group {
  display: flex;
  flex-direction: column;

  label {
    font-weight: 600;
    color: #34495e;
    margin-bottom: 8px;
    font-size: 0.9rem;
  }
}

.form-control {
  padding: 10px 12px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 0.95rem;
  transition: all 0.3s ease;

  &:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
  }
}

.stats-section {
  margin-bottom: 25px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 20px;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 25px;
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);

  h4 {
    margin: 0 0 10px 0;
    font-size: 1rem;
    opacity: 0.9;
  }

  .stat-value {
    font-size: 1.8rem;
    font-weight: bold;
    margin: 0;
  }

  small {
    font-size: 0.8rem;
    opacity: 0.8;
    margin-top: 5px;
    display: block;
  }
}

.actions-section {
  background: #ffffff;
  padding: 25px;
  border-radius: 10px;
  margin-bottom: 25px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  border-left: 4px solid #e74c3c;

  h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.3rem;
  }
}

.declaration-info {
  background: #e8f4fd;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
  border-left: 4px solid #3498db;

  p {
    margin: 0;
    color: #2c3e50;
    font-size: 1rem;
  }
}

.action-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
  }

  &.btn-primary {
    background: #3498db;
    color: white;

    &:hover {
      background: #2980b9;
    }
  }

  &.btn-success {
    background: #27ae60;
    color: white;

    &:hover {
      background: #229954;
    }
  }

  &.btn-secondary {
    background: #6c757d;
    color: white;

    &:hover {
      background: #5a6268;
    }
  }

  &.btn-large {
    padding: 15px 30px;
    font-size: 1.1rem;
    font-weight: 700;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
  }
}

// Spinners et indicateurs de chargement
.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-large {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

.loading-container {
  text-align: center;
  padding: 60px 20px;

  p {
    margin-top: 20px;
    color: #6c757d;
    font-size: 1.1rem;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.operations-section {
  background: #ffffff;
  padding: 25px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);

  h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.3rem;
  }
}

.table-container {
  overflow-x: auto;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.operations-table {
  width: 100%;
  border-collapse: collapse;
  background: white;

  thead {
    background: #34495e;
    color: white;

    th {
      padding: 15px 12px;
      text-align: left;
      font-weight: 600;
      font-size: 0.9rem;
      border-bottom: 2px solid #2c3e50;
    }
  }

  tbody {
    tr {
      transition: background-color 0.2s ease;

      &:nth-child(even) {
        background: #f8f9fa;
      }

      &:hover {
        background: #e3f2fd;
      }

      td {
        padding: 12px;
        border-bottom: 1px solid #dee2e6;
        font-size: 0.9rem;
      }
    }
  }
}

.badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;

  &.badge-achat {
    background: #d4edda;
    color: #155724;
  }

  &.badge-vente {
    background: #f8d7da;
    color: #721c24;
  }
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;

  &.status-en_attente {
    background: #fff3cd;
    color: #856404;
  }

  &.status-validee {
    background: #d4edda;
    color: #155724;
  }

  &.status-declaree {
    background: #cce5ff;
    color: #004085;
  }

  &.status-annulee {
    background: #f8d7da;
    color: #721c24;
  }
}

.no-data {
  text-align: center;
  padding: 40px;
  color: #6c757d;

  p {
    font-size: 1.1rem;
    margin: 0;
  }
}

// Responsive design
@media (max-width: 768px) {
  .operations-container {
    padding: 15px;
  }

  .filters-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    flex-direction: column;
  }

  .operations-table {
    font-size: 0.8rem;

    th, td {
      padding: 8px 6px;
    }
  }

  .header h1 {
    font-size: 2rem;
  }
}