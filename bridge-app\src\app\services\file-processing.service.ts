import { Injectable } from '@angular/core';
import { Observable, of, delay, throwError } from 'rxjs';
import { FichierDeclaration, ResultatImport, Operation } from '../models/operation.model';

@Injectable({
  providedIn: 'root'
})
export class FileProcessingService {

  private fichiersStockes: FichierDeclaration[] = [];

  constructor() { }

  // Traiter un fichier uploadé
  traiterFichier(file: File, bureauChangeId: string): Observable<ResultatImport> {
    const fichierDeclaration: FichierDeclaration = {
      id: this.generateId(),
      nom: file.name,
      taille: file.size,
      type: file.type,
      dateUpload: new Date(),
      statut: 'EN_ATTENTE',
      bureauChangeId,
      mois: new Date().getMonth() + 1,
      annee: new Date().getFullYear()
    };

    // Simuler le traitement du fichier
    return new Observable(observer => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const contenu = e.target?.result as string;
          const resultat = this.analyserContenuFichier(contenu, fichierDeclaration);
          
          // Stocker le fichier traité
          this.fichiersStockes.push(resultat.fichier);
          
          observer.next(resultat);
          observer.complete();
        } catch (error) {
          const resultatErreur: ResultatImport = {
            success: false,
            fichier: { ...fichierDeclaration, statut: 'ERREUR', erreurs: ['Erreur lors de la lecture du fichier'] },
            operationsImportees: [],
            erreurs: ['Erreur lors de la lecture du fichier: ' + (error as Error).message],
            avertissements: [],
            statistiques: {
              totalLignes: 0,
              lignesValides: 0,
              lignesErreur: 1,
              montantTotal: 0
            }
          };
          observer.next(resultatErreur);
          observer.complete();
        }
      };

      reader.onerror = () => {
        const resultatErreur: ResultatImport = {
          success: false,
          fichier: { ...fichierDeclaration, statut: 'ERREUR', erreurs: ['Impossible de lire le fichier'] },
          operationsImportees: [],
          erreurs: ['Impossible de lire le fichier'],
          avertissements: [],
          statistiques: {
            totalLignes: 0,
            lignesValides: 0,
            lignesErreur: 1,
            montantTotal: 0
          }
        };
        observer.next(resultatErreur);
        observer.complete();
      };

      reader.readAsText(file);
    }); // Simuler un délai de traitement
  }

  // Analyser le contenu du fichier CSV
  private analyserContenuFichier(contenu: string, fichier: FichierDeclaration): ResultatImport {
    const lignes = contenu.split('\n').filter(ligne => ligne.trim() !== '');
    const erreurs: string[] = [];
    const avertissements: string[] = [];
    const operationsImportees: Operation[] = [];

    if (lignes.length === 0) {
      return {
        success: false,
        fichier: { ...fichier, statut: 'ERREUR', erreurs: ['Fichier vide'] },
        operationsImportees: [],
        erreurs: ['Le fichier est vide'],
        avertissements: [],
        statistiques: {
          totalLignes: 0,
          lignesValides: 0,
          lignesErreur: 1,
          montantTotal: 0
        }
      };
    }

    // Vérifier l'en-tête (première ligne)
    const entete = lignes[0].split(',');
    const entetesAttendus = ['Date', 'Type', 'Devise', 'Montant Devise', 'Taux Change', 'Montant TND', 'Client', 'CIN', 'Numéro Opération'];
    
    if (!this.verifierEntetes(entete, entetesAttendus)) {
      avertissements.push('Format d\'en-tête non standard détecté');
    }

    // Traiter chaque ligne de données
    for (let i = 1; i < lignes.length; i++) {
      try {
        const operation = this.traiterLigneOperation(lignes[i], i + 1, fichier.bureauChangeId);
        if (operation) {
          operationsImportees.push(operation);
        }
      } catch (error) {
        erreurs.push(`Ligne ${i + 1}: ${(error as Error).message}`);
      }
    }

    const montantTotal = operationsImportees.reduce((sum, op) => sum + op.montantTND, 0);
    const success = erreurs.length === 0 && operationsImportees.length > 0;

    const fichierMisAJour: FichierDeclaration = {
      ...fichier,
      statut: success ? 'VALIDE' : (operationsImportees.length > 0 ? 'TRAITE' : 'ERREUR'),
      nombreOperations: operationsImportees.length,
      montantTotal,
      erreurs: erreurs.length > 0 ? erreurs : undefined,
      operationsImportees,
      dateTraitement: new Date()
    };

    return {
      success,
      fichier: fichierMisAJour,
      operationsImportees,
      erreurs,
      avertissements,
      statistiques: {
        totalLignes: lignes.length - 1, // Exclure l'en-tête
        lignesValides: operationsImportees.length,
        lignesErreur: erreurs.length,
        montantTotal
      }
    };
  }

  // Traiter une ligne d'opération
  private traiterLigneOperation(ligne: string, numeroLigne: number, bureauChangeId: string): Operation | null {
    const colonnes = ligne.split(',').map(col => col.trim().replace(/"/g, ''));

    if (colonnes.length < 9) {
      throw new Error(`Nombre de colonnes insuffisant (${colonnes.length}/9)`);
    }

    // Validation et conversion des données
    const dateStr = colonnes[0];
    const type = colonnes[1].toUpperCase();
    const devise = colonnes[2].toUpperCase();
    const montantDeviseStr = colonnes[3];
    const tauxChangeStr = colonnes[4];
    const montantTNDStr = colonnes[5];
    const clientNom = colonnes[6];
    const clientCIN = colonnes[7];
    const numeroOperation = colonnes[8];

    // Validations
    if (!['ACHAT', 'VENTE'].includes(type)) {
      throw new Error(`Type d'opération invalide: ${type}`);
    }

    if (!['EUR', 'USD', 'GBP', 'CHF'].includes(devise)) {
      throw new Error(`Devise non supportée: ${devise}`);
    }

    const date = this.parseDate(dateStr);
    if (!date) {
      throw new Error(`Format de date invalide: ${dateStr}`);
    }

    const montantDevise = parseFloat(montantDeviseStr);
    const tauxChange = parseFloat(tauxChangeStr);
    const montantTND = parseFloat(montantTNDStr);

    if (isNaN(montantDevise) || montantDevise <= 0) {
      throw new Error(`Montant devise invalide: ${montantDeviseStr}`);
    }

    if (isNaN(tauxChange) || tauxChange <= 0) {
      throw new Error(`Taux de change invalide: ${tauxChangeStr}`);
    }

    if (isNaN(montantTND) || montantTND <= 0) {
      throw new Error(`Montant TND invalide: ${montantTNDStr}`);
    }

    if (!clientNom || clientNom.length < 2) {
      throw new Error('Nom du client requis');
    }

    if (!clientCIN || clientCIN.length !== 8) {
      throw new Error('CIN invalide (8 chiffres requis)');
    }

    // Créer l'opération
    const operation: Operation = {
      id: this.generateId(),
      date,
      type: type as 'ACHAT' | 'VENTE',
      devise: devise as 'EUR' | 'USD' | 'GBP' | 'CHF',
      montantDevise,
      tauxChange,
      montantTND,
      clientNom,
      clientCIN,
      clientNationalite: 'Tunisienne',
      bureauChangeId,
      numeroOperation: numeroOperation || `IMP-${Date.now()}-${numeroLigne}`,
      statut: 'EN_ATTENTE',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    return operation;
  }

  // Vérifier les en-têtes du fichier
  private verifierEntetes(entetes: string[], entetesAttendus: string[]): boolean {
    return entetesAttendus.every(enteteAttendu => 
      entetes.some(entete => entete.toLowerCase().includes(enteteAttendu.toLowerCase()))
    );
  }

  // Parser une date depuis différents formats
  private parseDate(dateStr: string): Date | null {
    // Formats supportés: DD/MM/YYYY, DD-MM-YYYY, YYYY-MM-DD
    const formats = [
      /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/, // DD/MM/YYYY
      /^(\d{1,2})-(\d{1,2})-(\d{4})$/,   // DD-MM-YYYY
      /^(\d{4})-(\d{1,2})-(\d{1,2})$/    // YYYY-MM-DD
    ];

    for (const format of formats) {
      const match = dateStr.match(format);
      if (match) {
        let jour, mois, annee;
        if (format === formats[2]) { // YYYY-MM-DD
          annee = parseInt(match[1]);
          mois = parseInt(match[2]) - 1; // Les mois commencent à 0 en JavaScript
          jour = parseInt(match[3]);
        } else { // DD/MM/YYYY ou DD-MM-YYYY
          jour = parseInt(match[1]);
          mois = parseInt(match[2]) - 1;
          annee = parseInt(match[3]);
        }

        const date = new Date(annee, mois, jour);
        if (date.getFullYear() === annee && date.getMonth() === mois && date.getDate() === jour) {
          return date;
        }
      }
    }

    return null;
  }

  // Obtenir la liste des fichiers stockés
  getFichiersStockes(): Observable<FichierDeclaration[]> {
    return of([...this.fichiersStockes]).pipe(delay(500));
  }

  // Supprimer un fichier
  supprimerFichier(id: string): Observable<boolean> {
    const index = this.fichiersStockes.findIndex(f => f.id === id);
    if (index !== -1) {
      this.fichiersStockes.splice(index, 1);
      return of(true).pipe(delay(300));
    }
    return of(false).pipe(delay(300));
  }

  // Générer un ID unique
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}
