<div class="file-upload-container">
  <div class="header">
    <h2>Gestion des Déclarations Manuelles</h2>
    <p class="subtitle">Importez vos fichiers de déclaration CSV ou Excel pour traitement automatique</p>
  </div>

  <!-- Messages d'état -->
  <div *ngIf="successMessage" class="alert alert-success">
    <i class="icon-check"></i>
    {{successMessage}}
  </div>

  <div *ngIf="errorMessage" class="alert alert-error">
    <i class="icon-warning"></i>
    {{errorMessage}}
  </div>

  <!-- Section d'upload -->
  <div class="upload-section">
    <h3>Importer un nouveau fichier</h3>

    <!-- Sélection du bureau de change -->
    <div class="bureau-selection">
      <label for="bureauSelect">Bureau de Change:</label>
      <select id="bureauSelect" [(ngModel)]="selectedBureauId" class="form-control">
        <option value="">Sélectionner un bureau</option>
        <option *ngFor="let bureau of bureauxChange" [value]="bureau.id">
          {{bureau.nom}}
        </option>
      </select>
    </div>

    <!-- Zone de drag & drop -->
    <div
      class="drop-zone"
      [class.drag-over]="dragOver"
      [class.has-file]="selectedFile"
      (dragover)="onDragOver($event)"
      (dragleave)="onDragLeave($event)"
      (drop)="onDrop($event)">

      <div *ngIf="!selectedFile" class="drop-zone-content">
        <i class="icon-upload"></i>
        <h4>Glissez-déposez votre fichier ici</h4>
        <p>ou</p>
        <label for="fileInput" class="btn btn-secondary">
          Parcourir les fichiers
        </label>
        <input
          type="file"
          id="fileInput"
          accept=".csv,.xlsx,.xls"
          (change)="onFileSelected($event)"
          style="display: none;">
        <p class="file-info">Formats supportés: CSV, Excel (.csv, .xlsx, .xls) - Taille max: 10MB</p>
      </div>

      <div *ngIf="selectedFile" class="selected-file">
        <i class="icon-file"></i>
        <div class="file-details">
          <h4>{{selectedFile.name}}</h4>
          <p>{{formatFileSize(selectedFile.size)}} - {{selectedFile.type || 'Type inconnu'}}</p>
        </div>
        <button class="btn btn-small btn-danger" (click)="selectedFile = null; resetFileInput()">
          <i class="icon-close"></i>
        </button>
      </div>
    </div>

    <!-- Bouton d'upload -->
    <div class="upload-actions">
      <button
        class="btn btn-primary btn-large"
        [disabled]="!selectedFile || !selectedBureauId || isUploading"
        (click)="uploadFile()">
        <span *ngIf="isUploading" class="spinner"></span>
        <i *ngIf="!isUploading" class="icon-upload"></i>
        {{isUploading ? 'Traitement en cours...' : 'Traiter le fichier'}}
      </button>
    </div>
  </div>

  <!-- Résultat du dernier traitement -->
  <div *ngIf="dernierResultat" class="result-section">
    <h3>Résultat du traitement</h3>
    <div class="result-card" [class]="dernierResultat.success ? 'success' : 'error'">
      <div class="result-header">
        <i [class]="dernierResultat.success ? 'icon-check' : 'icon-warning'"></i>
        <h4>{{dernierResultat.success ? 'Traitement réussi' : 'Erreurs détectées'}}</h4>
      </div>

      <div class="result-stats">
        <div class="stat">
          <span class="label">Total lignes:</span>
          <span class="value">{{dernierResultat.statistiques.totalLignes}}</span>
        </div>
        <div class="stat">
          <span class="label">Lignes valides:</span>
          <span class="value">{{dernierResultat.statistiques.lignesValides}}</span>
        </div>
        <div class="stat">
          <span class="label">Lignes en erreur:</span>
          <span class="value">{{dernierResultat.statistiques.lignesErreur}}</span>
        </div>
        <div class="stat">
          <span class="label">Montant total:</span>
          <span class="value">{{dernierResultat.statistiques.montantTotal | number:'1.2-2'}} TND</span>
        </div>
      </div>

      <!-- Erreurs -->
      <div *ngIf="dernierResultat.erreurs.length > 0" class="errors-section">
        <h5>Erreurs détectées:</h5>
        <ul>
          <li *ngFor="let erreur of dernierResultat.erreurs">{{erreur}}</li>
        </ul>
      </div>

      <!-- Avertissements -->
      <div *ngIf="dernierResultat.avertissements.length > 0" class="warnings-section">
        <h5>Avertissements:</h5>
        <ul>
          <li *ngFor="let avertissement of dernierResultat.avertissements">{{avertissement}}</li>
        </ul>
      </div>
    </div>
  </div>

  <!-- Liste des fichiers stockés -->
  <div class="files-section">
    <h3>Fichiers de déclaration stockés</h3>

    <!-- Indicateur de chargement -->
    <div *ngIf="isLoadingFiles" class="loading-container">
      <div class="spinner-large"></div>
      <p>Chargement des fichiers...</p>
    </div>

    <!-- Liste des fichiers -->
    <div *ngIf="!isLoadingFiles" class="files-list">
      <div *ngIf="fichiersStockes.length === 0" class="no-files">
        <i class="icon-folder-empty"></i>
        <p>Aucun fichier de déclaration stocké</p>
        <small>Importez votre premier fichier pour commencer</small>
      </div>

      <div *ngFor="let fichier of fichiersStockes" class="file-card">
        <div class="file-icon">
          <i class="icon-file-text"></i>
        </div>

        <div class="file-info">
          <h4>{{fichier.nom}}</h4>
          <div class="file-meta">
            <span class="meta-item">
              <i class="icon-calendar"></i>
              {{fichier.dateUpload | date:'dd/MM/yyyy HH:mm'}}
            </span>
            <span class="meta-item">
              <i class="icon-building"></i>
              {{getBureauNom(fichier.bureauChangeId)}}
            </span>
            <span class="meta-item">
              <i class="icon-resize"></i>
              {{formatFileSize(fichier.taille)}}
            </span>
          </div>

          <div *ngIf="fichier.nombreOperations" class="file-stats">
            <span class="stat-badge">
              {{fichier.nombreOperations}} opérations
            </span>
            <span class="stat-badge">
              {{fichier.montantTotal | number:'1.2-2'}} TND
            </span>
          </div>
        </div>

        <div class="file-status">
          <span class="status-badge" [class]="getStatutClass(fichier.statut)">
            {{fichier.statut}}
          </span>
        </div>

        <div class="file-actions">
          <button
            class="btn btn-small btn-danger"
            (click)="supprimerFichier(fichier.id)"
            title="Supprimer le fichier">
            <i class="icon-trash"></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Guide d'utilisation -->
  <div class="guide-section">
    <h3>Guide d'utilisation</h3>
    <div class="guide-content">
      <div class="guide-step">
        <div class="step-number">1</div>
        <div class="step-content">
          <h4>Préparez votre fichier CSV</h4>
          <p>Le fichier doit contenir les colonnes suivantes dans l'ordre:</p>
          <code>Date, Type, Devise, Montant Devise, Taux Change, Montant TND, Client, CIN, Numéro Opération</code>
        </div>
      </div>

      <div class="guide-step">
        <div class="step-number">2</div>
        <div class="step-content">
          <h4>Sélectionnez le bureau de change</h4>
          <p>Choisissez le bureau de change correspondant aux opérations du fichier.</p>
        </div>
      </div>

      <div class="guide-step">
        <div class="step-number">3</div>
        <div class="step-content">
          <h4>Importez le fichier</h4>
          <p>Glissez-déposez votre fichier ou utilisez le bouton "Parcourir". Le système validera automatiquement le contenu.</p>
        </div>
      </div>

      <div class="guide-step">
        <div class="step-number">4</div>
        <div class="step-content">
          <h4>Vérifiez les résultats</h4>
          <p>Consultez le rapport de traitement pour identifier d'éventuelles erreurs à corriger.</p>
        </div>
      </div>
    </div>
  </div>
</div>
