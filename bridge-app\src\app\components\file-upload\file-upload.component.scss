.file-upload-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 30px;

  h2 {
    color: #2c3e50;
    font-size: 2.2rem;
    margin-bottom: 10px;
    font-weight: 600;
  }

  .subtitle {
    color: #7f8c8d;
    font-size: 1.1rem;
    margin: 0;
  }
}

// Messages d'alerte
.alert {
  padding: 15px 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 500;

  &.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }

  &.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }
}

// Section d'upload
.upload-section {
  background: #ffffff;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 30px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  border-left: 4px solid #3498db;

  h3 {
    color: #2c3e50;
    margin-bottom: 25px;
    font-size: 1.4rem;
  }
}

.bureau-selection {
  margin-bottom: 25px;

  label {
    display: block;
    font-weight: 600;
    color: #34495e;
    margin-bottom: 8px;
  }

  .form-control {
    width: 100%;
    max-width: 400px;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;

    &:focus {
      outline: none;
      border-color: #3498db;
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }
  }
}

// Zone de drag & drop
.drop-zone {
  border: 3px dashed #bdc3c7;
  border-radius: 12px;
  padding: 40px 20px;
  text-align: center;
  transition: all 0.3s ease;
  background: #f8f9fa;
  margin-bottom: 20px;

  &.drag-over {
    border-color: #3498db;
    background: #e3f2fd;
    transform: scale(1.02);
  }

  &.has-file {
    border-color: #27ae60;
    background: #e8f5e8;
  }
}

.drop-zone-content {
  i {
    font-size: 3rem;
    color: #95a5a6;
    margin-bottom: 20px;
    display: block;
  }

  h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.3rem;
  }

  p {
    color: #7f8c8d;
    margin: 10px 0;
    font-size: 1rem;
  }

  .file-info {
    font-size: 0.9rem;
    color: #95a5a6;
    margin-top: 15px;
  }
}

.selected-file {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #27ae60;

  i {
    font-size: 2rem;
    color: #27ae60;
  }

  .file-details {
    flex: 1;

    h4 {
      margin: 0 0 5px 0;
      color: #2c3e50;
      font-size: 1.1rem;
    }

    p {
      margin: 0;
      color: #7f8c8d;
      font-size: 0.9rem;
    }
  }
}

.upload-actions {
  text-align: center;

  .btn-large {
    padding: 15px 40px;
    font-size: 1.1rem;
    font-weight: 600;
  }
}

// Boutons
.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
  }

  &.btn-primary {
    background: #3498db;
    color: white;

    &:hover {
      background: #2980b9;
    }
  }

  &.btn-secondary {
    background: #6c757d;
    color: white;

    &:hover {
      background: #5a6268;
    }
  }

  &.btn-danger {
    background: #e74c3c;
    color: white;

    &:hover {
      background: #c0392b;
    }
  }

  &.btn-small {
    padding: 6px 12px;
    font-size: 0.85rem;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
  }
}

// Spinners
.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-large {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

.loading-container {
  text-align: center;
  padding: 40px 20px;

  p {
    margin-top: 15px;
    color: #6c757d;
    font-size: 1rem;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Section de résultats
.result-section {
  margin-bottom: 30px;

  h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.4rem;
  }
}

.result-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);

  &.success {
    border-left: 4px solid #27ae60;
  }

  &.error {
    border-left: 4px solid #e74c3c;
  }
}

.result-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;

  i {
    font-size: 1.5rem;
  }

  .success & i {
    color: #27ae60;
  }

  .error & i {
    color: #e74c3c;
  }

  h4 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.2rem;
  }
}

.result-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;

  .stat {
    display: flex;
    justify-content: space-between;
    padding: 10px 15px;
    background: #f8f9fa;
    border-radius: 6px;

    .label {
      font-weight: 500;
      color: #6c757d;
    }

    .value {
      font-weight: 600;
      color: #2c3e50;
    }
  }
}

.errors-section, .warnings-section {
  margin-top: 20px;

  h5 {
    color: #e74c3c;
    margin-bottom: 10px;
    font-size: 1rem;
  }

  ul {
    margin: 0;
    padding-left: 20px;

    li {
      color: #721c24;
      margin-bottom: 5px;
      font-size: 0.9rem;
    }
  }
}

.warnings-section {
  h5 {
    color: #f39c12;
  }

  ul li {
    color: #856404;
  }
}

// Section des fichiers stockés
.files-section {
  background: white;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 30px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);

  h3 {
    color: #2c3e50;
    margin-bottom: 25px;
    font-size: 1.4rem;
  }
}

.no-files {
  text-align: center;
  padding: 60px 20px;
  color: #95a5a6;

  i {
    font-size: 4rem;
    margin-bottom: 20px;
    display: block;
  }

  p {
    font-size: 1.2rem;
    margin-bottom: 10px;
  }

  small {
    font-size: 1rem;
  }
}

.files-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.file-card {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  border: 1px solid #e9ecef;
  border-radius: 10px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transform: translateY(-2px);
  }
}

.file-icon {
  i {
    font-size: 2.5rem;
    color: #3498db;
  }
}

.file-info {
  flex: 1;

  h4 {
    margin: 0 0 8px 0;
    color: #2c3e50;
    font-size: 1.1rem;
  }

  .file-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 10px;

    .meta-item {
      display: flex;
      align-items: center;
      gap: 5px;
      color: #6c757d;
      font-size: 0.9rem;

      i {
        font-size: 0.8rem;
      }
    }
  }

  .file-stats {
    display: flex;
    gap: 10px;

    .stat-badge {
      background: #e3f2fd;
      color: #1976d2;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 0.8rem;
      font-weight: 500;
    }
  }
}

.file-status {
  .status-badge {
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;

    &.status-valide {
      background: #d4edda;
      color: #155724;
    }

    &.status-traite {
      background: #fff3cd;
      color: #856404;
    }

    &.status-erreur {
      background: #f8d7da;
      color: #721c24;
    }

    &.status-attente {
      background: #cce5ff;
      color: #004085;
    }
  }
}

.file-actions {
  display: flex;
  gap: 8px;
}

// Guide d'utilisation
.guide-section {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 12px;
  border-left: 4px solid #17a2b8;

  h3 {
    color: #2c3e50;
    margin-bottom: 25px;
    font-size: 1.4rem;
  }
}

.guide-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.guide-step {
  display: flex;
  gap: 20px;
  align-items: flex-start;

  .step-number {
    width: 40px;
    height: 40px;
    background: #17a2b8;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.1rem;
    flex-shrink: 0;
  }

  .step-content {
    flex: 1;

    h4 {
      margin: 0 0 8px 0;
      color: #2c3e50;
      font-size: 1.1rem;
    }

    p {
      margin: 0 0 10px 0;
      color: #6c757d;
      line-height: 1.5;
    }

    code {
      background: #e9ecef;
      padding: 8px 12px;
      border-radius: 4px;
      font-family: 'Courier New', monospace;
      font-size: 0.9rem;
      color: #495057;
      display: block;
      margin-top: 8px;
      word-break: break-all;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .file-upload-container {
    padding: 15px;
  }

  .upload-section, .files-section, .guide-section {
    padding: 20px;
  }

  .header h2 {
    font-size: 1.8rem;
  }

  .drop-zone {
    padding: 30px 15px;
  }

  .drop-zone-content {
    i {
      font-size: 2.5rem;
    }

    h4 {
      font-size: 1.1rem;
    }
  }

  .result-stats {
    grid-template-columns: 1fr;
  }

  .file-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;

    .file-info .file-meta {
      flex-direction: column;
      gap: 8px;
    }

    .file-actions {
      align-self: flex-end;
    }
  }

  .guide-step {
    flex-direction: column;
    gap: 15px;

    .step-number {
      align-self: flex-start;
    }

    .step-content code {
      font-size: 0.8rem;
      padding: 6px 8px;
    }
  }

  .bureau-selection .form-control {
    max-width: 100%;
  }

  .btn-large {
    padding: 12px 30px !important;
    font-size: 1rem !important;
  }
}