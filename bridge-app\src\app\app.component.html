<div class="app-container">
  <!-- En-tête de l'application -->
  <header class="app-header">
    <div class="header-content">
      <div class="header-left">
        <div class="logo-section">
          <div class="logo-icon">
            <i class="icon-bank"></i>
          </div>
          <div class="logo-text">
            <h1>Bridge</h1>
            <span class="logo-tagline">Exchange Operations</span>
          </div>
        </div>
      </div>

      <div class="header-center">
        <div class="title-section">
          <h2>Gestion des Opérations de Change</h2>
          <p class="subtitle">Système de déclaration automatisée - Banque Centrale de Tunisie</p>
        </div>
      </div>

      <div class="header-right">
        <div class="user-section">
          <div class="user-info">
            <span class="user-name">Utilisateur</span>
            <span class="user-role">Administrateur</span>
          </div>
          <div class="user-avatar">
            <i class="icon-user"></i>
          </div>
        </div>
        <div class="header-actions">
          <button class="btn-header" title="Notifications">
            <i class="icon-bell"></i>
          </button>
          <button class="btn-header" title="Paramètres">
            <i class="icon-settings"></i>
          </button>
        </div>
      </div>
    </div>
  </header>

  <!-- Navigation par onglets -->
  <nav class="tab-navigation">
    <div class="tab-container">
      <button
        class="tab-button"
        [class.active]="activeTab === 'operations'"
        (click)="setActiveTab('operations')">
        <i class="icon-list"></i>
        Gestion des Opérations
      </button>
      <button
        class="tab-button"
        [class.active]="activeTab === 'files'"
        (click)="setActiveTab('files')">
        <i class="icon-upload"></i>
        Déclarations Manuelles
      </button>
    </div>
  </nav>

  <!-- Contenu des onglets -->
  <main class="tab-content">
    <div *ngIf="activeTab === 'operations'" class="tab-panel">
      <app-operations-list></app-operations-list>
    </div>

    <div *ngIf="activeTab === 'files'" class="tab-panel">
      <app-file-upload></app-file-upload>
    </div>
  </main>
</div>


