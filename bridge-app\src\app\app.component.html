<div class="app-container">
  <!-- En-tête de l'application -->
  <header class="app-header">
    <div class="header-content">
      <h1>Bridge - Gestion des Opérations de Change</h1>
      <p class="subtitle">Système de déclaration automatisée pour la Banque Centrale de Tunisie</p>
    </div>
  </header>

  <!-- Navigation par onglets -->
  <nav class="tab-navigation">
    <div class="tab-container">
      <button
        class="tab-button"
        [class.active]="activeTab === 'operations'"
        (click)="setActiveTab('operations')">
        <i class="icon-list"></i>
        Gestion des Opérations
      </button>
      <button
        class="tab-button"
        [class.active]="activeTab === 'files'"
        (click)="setActiveTab('files')">
        <i class="icon-upload"></i>
        Déclarations Manuelles
      </button>
    </div>
  </nav>

  <!-- Contenu des onglets -->
  <main class="tab-content">
    <div *ngIf="activeTab === 'operations'" class="tab-panel">
      <app-operations-list></app-operations-list>
    </div>

    <div *ngIf="activeTab === 'files'" class="tab-panel">
      <app-file-upload></app-file-upload>
    </div>
  </main>
</div>


