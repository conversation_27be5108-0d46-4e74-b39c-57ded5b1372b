import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import { OperationsListComponent } from './components/operations-list/operations-list.component';
import { FileUploadComponent } from './components/file-upload/file-upload.component';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [CommonModule, RouterOutlet, OperationsListComponent, FileUploadComponent],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent {
  title = 'Bridge - Gestion des Opérations de Change';
  activeTab = 'operations'; // 'operations' ou 'files'

  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }
}
