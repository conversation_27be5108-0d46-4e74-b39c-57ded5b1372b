.app-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.app-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 30px 0;
  box-shadow: 0 2px 20px rgba(0,0,0,0.1);

  .header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    text-align: center;

    h1 {
      color: #2c3e50;
      font-size: 2.5rem;
      margin-bottom: 10px;
      font-weight: 700;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .subtitle {
      color: #7f8c8d;
      font-size: 1.1rem;
      margin: 0;
      font-weight: 400;
    }
  }
}

.tab-navigation {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 100;

  .tab-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    gap: 0;
  }
}

.tab-button {
  background: transparent;
  border: none;
  padding: 20px 30px;
  font-size: 1rem;
  font-weight: 600;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;

  &:hover {
    color: #495057;
    background: rgba(52, 152, 219, 0.05);
  }

  &.active {
    color: #3498db;
    border-bottom-color: #3498db;
    background: rgba(52, 152, 219, 0.1);

    &::after {
      content: '';
      position: absolute;
      bottom: -1px;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, #3498db, #2980b9);
      border-radius: 2px 2px 0 0;
    }
  }

  i {
    font-size: 1.1rem;
  }
}

.tab-content {
  background: transparent;
  min-height: calc(100vh - 200px);
}

.tab-panel {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive design
@media (max-width: 768px) {
  .app-header {
    padding: 20px 0;

    .header-content {
      padding: 0 15px;

      h1 {
        font-size: 2rem;
      }

      .subtitle {
        font-size: 1rem;
      }
    }
  }

  .tab-navigation .tab-container {
    padding: 0 15px;
    flex-direction: column;
  }

  .tab-button {
    padding: 15px 20px;
    justify-content: center;
    border-bottom: none;
    border-right: 3px solid transparent;

    &.active {
      border-bottom: none;
      border-right-color: #3498db;

      &::after {
        display: none;
      }
    }
  }
}