.app-container {
  min-height: 100vh;
  background: #ffffff;
}

.app-header {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  padding: 20px 0;
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
  border-bottom: 3px solid #3498db;

  .header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
  }
}

.header-left {
  flex: 0 0 auto;

  .logo-section {
    display: flex;
    align-items: center;
    gap: 15px;

    .logo-icon {
      width: 50px;
      height: 50px;
      background: linear-gradient(135deg, #3498db, #2980b9);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);

      i {
        font-size: 24px;
        color: white;
      }
    }

    .logo-text {
      h1 {
        color: white;
        font-size: 1.8rem;
        margin: 0;
        font-weight: 700;
        letter-spacing: -0.5px;
      }

      .logo-tagline {
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.85rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 1px;
      }
    }
  }
}

.header-center {
  flex: 1;
  text-align: center;

  .title-section {
    h2 {
      color: white;
      font-size: 1.6rem;
      margin: 0 0 5px 0;
      font-weight: 600;
      letter-spacing: -0.3px;
    }

    .subtitle {
      color: rgba(255, 255, 255, 0.85);
      font-size: 0.95rem;
      margin: 0;
      font-weight: 400;
    }
  }
}

.header-right {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  gap: 20px;

  .user-section {
    display: flex;
    align-items: center;
    gap: 12px;

    .user-info {
      text-align: right;

      .user-name {
        display: block;
        color: white;
        font-size: 0.9rem;
        font-weight: 600;
        line-height: 1.2;
      }

      .user-role {
        display: block;
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.8rem;
        font-weight: 400;
      }
    }

    .user-avatar {
      width: 40px;
      height: 40px;
      background: rgba(255, 255, 255, 0.15);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2px solid rgba(255, 255, 255, 0.2);

      i {
        font-size: 18px;
        color: white;
      }
    }
  }

  .header-actions {
    display: flex;
    gap: 8px;

    .btn-header {
      width: 36px;
      height: 36px;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-1px);
      }

      i {
        font-size: 16px;
        color: white;
      }
    }
  }
}

.tab-navigation {
  background: #ffffff;
  border-bottom: 1px solid #e9ecef;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);

  .tab-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    gap: 0;
  }
}

.tab-button {
  background: transparent;
  border: none;
  padding: 20px 30px;
  font-size: 1rem;
  font-weight: 600;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;

  &:hover {
    color: #495057;
    background: rgba(52, 152, 219, 0.05);
  }

  &.active {
    color: #3498db;
    border-bottom-color: #3498db;
    background: rgba(52, 152, 219, 0.1);

    &::after {
      content: '';
      position: absolute;
      bottom: -1px;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, #3498db, #2980b9);
      border-radius: 2px 2px 0 0;
    }
  }

  i {
    font-size: 1.1rem;
  }
}

.tab-content {
  background: #f8f9fa;
  min-height: calc(100vh - 200px);
}

.tab-panel {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive design
@media (max-width: 768px) {
  .app-header {
    padding: 15px 0;

    .header-content {
      padding: 0 15px;
      flex-direction: column;
      gap: 15px;
    }
  }

  .header-left {
    .logo-section {
      .logo-icon {
        width: 40px;
        height: 40px;

        i {
          font-size: 20px;
        }
      }

      .logo-text {
        h1 {
          font-size: 1.5rem;
        }

        .logo-tagline {
          font-size: 0.75rem;
        }
      }
    }
  }

  .header-center {
    .title-section {
      h2 {
        font-size: 1.3rem;
      }

      .subtitle {
        font-size: 0.85rem;
      }
    }
  }

  .header-right {
    flex-direction: row;
    justify-content: center;

    .user-section {
      .user-info {
        display: none; // Masquer les infos utilisateur sur mobile
      }
    }
  }
}

@media (max-width: 1024px) and (min-width: 769px) {
  .header-center {
    .title-section {
      h2 {
        font-size: 1.4rem;
      }

      .subtitle {
        font-size: 0.9rem;
      }
    }
  }

  .header-right {
    .user-section {
      .user-info {
        .user-name {
          font-size: 0.85rem;
        }

        .user-role {
          font-size: 0.75rem;
        }
      }
    }
  }
}

  .tab-navigation .tab-container {
    padding: 0 15px;
    flex-direction: column;
  }

  .tab-button {
    padding: 15px 20px;
    justify-content: center;
    border-bottom: none;
    border-right: 3px solid transparent;

    &.active {
      border-bottom: none;
      border-right-color: #3498db;

      &::after {
        display: none;
      }
    }
  }
}