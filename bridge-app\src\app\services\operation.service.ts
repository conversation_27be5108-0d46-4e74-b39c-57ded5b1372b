import { Injectable } from '@angular/core';
import { Observable, of, map, catchError } from 'rxjs';
import { Operation, BureauChange, DeclarationMensuelle, FilterCriteria, StatistiquesOperations } from '../models/operation.model';
import { FakeApiService } from './fake-api.service';

@Injectable({
  providedIn: 'root'
})
export class OperationService {

  constructor(private fakeApiService: FakeApiService) { }

  // État de chargement pour l'interface utilisateur
  private _isLoading = false;
  private _error: string | null = null;

  get isLoading(): boolean {
    return this._isLoading;
  }

  get error(): string | null {
    return this._error;
  }

  getOperations(filter?: FilterCriteria): Observable<Operation[]> {
    this._isLoading = true;
    this._error = null;

    return this.fakeApiService.getOperations(filter).pipe(
      map(response => {
        this._isLoading = false;
        if (response.success) {
          return response.data;
        } else {
          throw new Error('Erreur lors du chargement des opérations');
        }
      }),
      catchError(error => {
        this._isLoading = false;
        this._error = error.message || 'Erreur de connexion';
        throw error;
      })
    );
  }

  getBureauxChange(): Observable<BureauChange[]> {
    this._isLoading = true;
    this._error = null;

    return this.fakeApiService.getBureauxChange().pipe(
      map(response => {
        this._isLoading = false;
        if (response.success) {
          return response.data;
        } else {
          throw new Error('Erreur lors du chargement des bureaux de change');
        }
      }),
      catchError(error => {
        this._isLoading = false;
        this._error = error.message || 'Erreur de connexion';
        throw error;
      })
    );
  }

  getStatistiques(filter?: FilterCriteria): Observable<StatistiquesOperations> {
    return this.getOperations(filter).pipe(
      map(operations => {
        const stats: StatistiquesOperations = {
          totalOperations: operations.length,
          totalMontantTND: operations.reduce((sum, op) => sum + op.montantTND, 0),
          totalMontantDevise: operations.reduce((sum, op) => sum + op.montantDevise, 0),
          repartitionParDevise: {},
          repartitionParType: { achat: 0, vente: 0 },
          evolutionMensuelle: []
        };

        // Calcul des répartitions
        operations.forEach(op => {
          stats.repartitionParDevise[op.devise] = (stats.repartitionParDevise[op.devise] || 0) + op.montantTND;
          if (op.type === 'ACHAT') {
            stats.repartitionParType.achat += op.montantTND;
          } else {
            stats.repartitionParType.vente += op.montantTND;
          }
        });

        return stats;
      })
    );
  }

  // Nouvelle méthode pour déclarer toutes les opérations d'un mois
  declarerOperationsMensuelle(bureauId: string, mois: number, annee: number): Observable<{
    declaration: DeclarationMensuelle,
    operationsDeclarees: number,
    success: boolean,
    message: string
  }> {
    this._isLoading = true;
    this._error = null;

    return this.fakeApiService.declarerOperationsMensuelle(bureauId, mois, annee).pipe(
      map(response => {
        this._isLoading = false;
        return response;
      }),
      catchError(error => {
        this._isLoading = false;
        this._error = error.error || 'Erreur lors de la déclaration';
        throw error;
      })
    );
  }

  // Méthode pour vérifier le statut d'une déclaration
  getStatutDeclaration(declarationId: string): Observable<{
    statut: 'SOUMISE' | 'EN_COURS' | 'VALIDEE' | 'REJETEE',
    message: string,
    dateTraitement?: Date,
    success: boolean
  }> {
    return this.fakeApiService.getStatutDeclaration(declarationId);
  }

  genererDeclarationMensuelle(bureauChangeId: string, mois: number, annee: number): Observable<DeclarationMensuelle> {
    const filter: FilterCriteria = {
      bureauChangeId,
      mois,
      annee,
      statut: 'VALIDEE' // Seules les opérations validées peuvent être déclarées
    };

    return this.getOperations(filter).pipe(
      map(operations => {
        const declaration: DeclarationMensuelle = {
          id: `DECL-${bureauChangeId}-${annee}-${mois.toString().padStart(2, '0')}`,
          bureauChangeId,
          mois,
          annee,
          operations,
          totalAchats: operations.filter(op => op.type === 'ACHAT').reduce((sum, op) => sum + op.montantTND, 0),
          totalVentes: operations.filter(op => op.type === 'VENTE').reduce((sum, op) => sum + op.montantTND, 0),
          totalOperations: operations.length,
          statut: 'BROUILLON',
          dateCreation: new Date()
        };

        return declaration;
      })
    );
  }

  exporterDeclaration(declaration: DeclarationMensuelle): void {
    const csvContent = this.convertToCSV(declaration);
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `declaration_${declaration.bureauChangeId}_${declaration.annee}_${declaration.mois}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  private convertToCSV(declaration: DeclarationMensuelle): string {
    const headers = ['Date', 'Type', 'Devise', 'Montant Devise', 'Taux Change', 'Montant TND', 'Client', 'CIN', 'Numéro Opération'];
    const csvArray = [headers.join(',')];

    declaration.operations.forEach(op => {
      const row = [
        op.date.toLocaleDateString(),
        op.type,
        op.devise,
        op.montantDevise.toString(),
        op.tauxChange.toString(),
        op.montantTND.toString(),
        op.clientNom,
        op.clientCIN,
        op.numeroOperation
      ];
      csvArray.push(row.join(','));
    });

    return csvArray.join('\n');
  }
}
