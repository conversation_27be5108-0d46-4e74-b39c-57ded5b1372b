import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Operation, BureauChange, DeclarationMensuelle, FilterCriteria, StatistiquesOperations } from '../models/operation.model';

@Injectable({
  providedIn: 'root'
})
export class OperationService {

  private fakeOperations: Operation[] = [
    {
      id: '1',
      date: new Date('2024-01-15'),
      type: 'ACHAT',
      devise: 'EUR',
      montantDevise: 1000,
      tauxChange: 3.25,
      montantTND: 3250,
      clientNom: '<PERSON>',
      clientCIN: '12345678',
      clientNationalite: 'Tunisienne',
      bureauChangeId: 'BC001',
      numeroOperation: 'OP-2024-001',
      statut: 'VALIDEE',
      createdAt: new Date('2024-01-15'),
      updatedAt: new Date('2024-01-15')
    },
    {
      id: '2',
      date: new Date('2024-01-16'),
      type: 'VENTE',
      devise: 'EUR',
      montantDevise: 500,
      tauxChange: 3.20,
      montantTND: 1600,
      clientNom: '<PERSON><PERSON>',
      clientCIN: '87654321',
      clientNationalite: 'Tunisienne',
      bureauChangeId: 'BC001',
      numeroOperation: 'OP-2024-002',
      statut: 'VALIDEE',
      createdAt: new Date('2024-01-16'),
      updatedAt: new Date('2024-01-16')
    },
    {
      id: '3',
      date: new Date('2024-02-10'),
      type: 'ACHAT',
      devise: 'USD',
      montantDevise: 800,
      tauxChange: 3.10,
      montantTND: 2480,
      clientNom: 'Mohamed Sassi',
      clientCIN: '11223344',
      clientNationalite: 'Tunisienne',
      bureauChangeId: 'BC001',
      numeroOperation: 'OP-2024-003',
      statut: 'DECLAREE',
      createdAt: new Date('2024-02-10'),
      updatedAt: new Date('2024-02-10')
    },
    {
      id: '4',
      date: new Date('2024-02-15'),
      type: 'VENTE',
      devise: 'EUR',
      montantDevise: 1200,
      tauxChange: 3.22,
      montantTND: 3864,
      clientNom: 'Leila Bouazizi',
      clientCIN: '55667788',
      clientNationalite: 'Tunisienne',
      bureauChangeId: 'BC001',
      numeroOperation: 'OP-2024-004',
      statut: 'VALIDEE',
      createdAt: new Date('2024-02-15'),
      updatedAt: new Date('2024-02-15')
    },
    {
      id: '5',
      date: new Date('2024-03-05'),
      type: 'ACHAT',
      devise: 'EUR',
      montantDevise: 2000,
      tauxChange: 3.28,
      montantTND: 6560,
      clientNom: 'Karim Jemli',
      clientCIN: '99887766',
      clientNationalite: 'Tunisienne',
      bureauChangeId: 'BC001',
      numeroOperation: 'OP-2024-005',
      statut: 'EN_ATTENTE',
      createdAt: new Date('2024-03-05'),
      updatedAt: new Date('2024-03-05')
    },
    {
      id: '6',
      date: new Date('2024-01-20'),
      type: 'VENTE',
      devise: 'USD',
      montantDevise: 750,
      tauxChange: 3.08,
      montantTND: 2310,
      clientNom: 'Sonia Gharbi',
      clientCIN: '44556677',
      clientNationalite: 'Tunisienne',
      bureauChangeId: 'BC001',
      numeroOperation: 'OP-2024-006',
      statut: 'VALIDEE',
      createdAt: new Date('2024-01-20'),
      updatedAt: new Date('2024-01-20')
    },
    {
      id: '7',
      date: new Date('2024-02-25'),
      type: 'ACHAT',
      devise: 'GBP',
      montantDevise: 600,
      tauxChange: 3.85,
      montantTND: 2310,
      clientNom: 'Nabil Khelifi',
      clientCIN: '33445566',
      clientNationalite: 'Tunisienne',
      bureauChangeId: 'BC001',
      numeroOperation: 'OP-2024-007',
      statut: 'DECLAREE',
      createdAt: new Date('2024-02-25'),
      updatedAt: new Date('2024-02-25')
    },
    {
      id: '8',
      date: new Date('2024-01-30'),
      type: 'VENTE',
      devise: 'CHF',
      montantDevise: 400,
      tauxChange: 3.45,
      montantTND: 1380,
      clientNom: 'Rim Bouslama',
      clientCIN: '77889900',
      clientNationalite: 'Tunisienne',
      bureauChangeId: 'BC001',
      numeroOperation: 'OP-2024-008',
      statut: 'VALIDEE',
      createdAt: new Date('2024-01-30'),
      updatedAt: new Date('2024-01-30')
    },
    {
      id: '9',
      date: new Date('2024-03-10'),
      type: 'ACHAT',
      devise: 'EUR',
      montantDevise: 1500,
      tauxChange: 3.30,
      montantTND: 4950,
      clientNom: 'Youssef Mrad',
      clientCIN: '11335577',
      clientNationalite: 'Tunisienne',
      bureauChangeId: 'BC001',
      numeroOperation: 'OP-2024-009',
      statut: 'EN_ATTENTE',
      createdAt: new Date('2024-03-10'),
      updatedAt: new Date('2024-03-10')
    },
    {
      id: '10',
      date: new Date('2024-02-18'),
      type: 'VENTE',
      devise: 'USD',
      montantDevise: 900,
      tauxChange: 3.12,
      montantTND: 2808,
      clientNom: 'Asma Dridi',
      clientCIN: '22446688',
      clientNationalite: 'Tunisienne',
      bureauChangeId: 'BC001',
      numeroOperation: 'OP-2024-010',
      statut: 'VALIDEE',
      createdAt: new Date('2024-02-18'),
      updatedAt: new Date('2024-02-18')
    }
  ];

  private fakeBureauxChange: BureauChange[] = [
    {
      id: 'BC001',
      nom: 'Bureau de Change Central',
      adresse: 'Avenue Habib Bourguiba',
      ville: 'Tunis',
      codePostal: '1000',
      telephone: '+216 71 123 456',
      email: '<EMAIL>',
      numeroLicence: 'BC-2020-001',
      responsable: 'Hedi Mansouri',
      statut: 'ACTIF',
      dateCreation: new Date('2020-01-01')
    }
  ];

  constructor() { }

  getOperations(filter?: FilterCriteria): Observable<Operation[]> {
    let filteredOperations = [...this.fakeOperations];

    if (filter) {
      if (filter.mois && filter.annee) {
        filteredOperations = filteredOperations.filter(op => 
          op.date.getMonth() + 1 === filter.mois && op.date.getFullYear() === filter.annee
        );
      }
      if (filter.devise) {
        filteredOperations = filteredOperations.filter(op => op.devise === filter.devise);
      }
      if (filter.type) {
        filteredOperations = filteredOperations.filter(op => op.type === filter.type);
      }
      if (filter.statut) {
        filteredOperations = filteredOperations.filter(op => op.statut === filter.statut);
      }
    }

    return of(filteredOperations);
  }

  getBureauxChange(): Observable<BureauChange[]> {
    return of(this.fakeBureauxChange);
  }

  getStatistiques(filter?: FilterCriteria): Observable<StatistiquesOperations> {
    const operations = this.fakeOperations;
    
    const stats: StatistiquesOperations = {
      totalOperations: operations.length,
      totalMontantTND: operations.reduce((sum, op) => sum + op.montantTND, 0),
      totalMontantDevise: operations.reduce((sum, op) => sum + op.montantDevise, 0),
      repartitionParDevise: {},
      repartitionParType: { achat: 0, vente: 0 },
      evolutionMensuelle: []
    };

    // Calcul des répartitions
    operations.forEach(op => {
      stats.repartitionParDevise[op.devise] = (stats.repartitionParDevise[op.devise] || 0) + op.montantTND;
      if (op.type === 'ACHAT') {
        stats.repartitionParType.achat += op.montantTND;
      } else {
        stats.repartitionParType.vente += op.montantTND;
      }
    });

    return of(stats);
  }

  genererDeclarationMensuelle(bureauChangeId: string, mois: number, annee: number): Observable<DeclarationMensuelle> {
    const operations = this.fakeOperations.filter(op => 
      op.bureauChangeId === bureauChangeId &&
      op.date.getMonth() + 1 === mois &&
      op.date.getFullYear() === annee
    );

    const declaration: DeclarationMensuelle = {
      id: `DECL-${bureauChangeId}-${annee}-${mois.toString().padStart(2, '0')}`,
      bureauChangeId,
      mois,
      annee,
      operations,
      totalAchats: operations.filter(op => op.type === 'ACHAT').reduce((sum, op) => sum + op.montantTND, 0),
      totalVentes: operations.filter(op => op.type === 'VENTE').reduce((sum, op) => sum + op.montantTND, 0),
      totalOperations: operations.length,
      statut: 'BROUILLON',
      dateCreation: new Date()
    };

    return of(declaration);
  }

  exporterDeclaration(declaration: DeclarationMensuelle): void {
    const csvContent = this.convertToCSV(declaration);
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `declaration_${declaration.bureauChangeId}_${declaration.annee}_${declaration.mois}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  private convertToCSV(declaration: DeclarationMensuelle): string {
    const headers = ['Date', 'Type', 'Devise', 'Montant Devise', 'Taux Change', 'Montant TND', 'Client', 'CIN', 'Numéro Opération'];
    const csvArray = [headers.join(',')];

    declaration.operations.forEach(op => {
      const row = [
        op.date.toLocaleDateString(),
        op.type,
        op.devise,
        op.montantDevise.toString(),
        op.tauxChange.toString(),
        op.montantTND.toString(),
        op.clientNom,
        op.clientCIN,
        op.numeroOperation
      ];
      csvArray.push(row.join(','));
    });

    return csvArray.join('\n');
  }
}
