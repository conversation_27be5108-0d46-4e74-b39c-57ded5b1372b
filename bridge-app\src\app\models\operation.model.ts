export interface Operation {
  id: string;
  date: Date;
  type: 'ACHAT' | 'VENTE';
  devise: 'EUR' | 'USD' | 'GBP' | 'CHF';
  montantDevise: number;
  tauxChange: number;
  montantTND: number;
  clientNom: string;
  clientCIN: string;
  clientPasseport?: string;
  clientNationalite: string;
  bureauChangeId: string;
  numeroOperation: string;
  statut: 'EN_ATTENTE' | 'VALIDEE' | 'DECLAREE' | 'ANNULEE';
  commentaire?: string;
  pieceJustificative?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface BureauChange {
  id: string;
  nom: string;
  adresse: string;
  ville: string;
  codePostal: string;
  telephone: string;
  email: string;
  numeroLicence: string;
  responsable: string;
  statut: 'ACTIF' | 'INACTIF' | 'SUSPENDU';
  dateCreation: Date;
}

export interface DeclarationMensuelle {
  id: string;
  bureauChangeId: string;
  mois: number;
  annee: number;
  operations: Operation[];
  totalAchats: number;
  totalVentes: number;
  totalOperations: number;
  statut: 'BROUILLON' | 'SOUMISE' | 'VALIDEE' | 'REJETEE';
  dateCreation: Date;
  dateSoumission?: Date;
  dateValidation?: Date;
  commentaireBCT?: string;
}

export interface FilterCriteria {
  bureauChangeId?: string;
  mois?: number;
  annee?: number;
  devise?: string;
  type?: 'ACHAT' | 'VENTE';
  statut?: string;
  dateDebut?: Date;
  dateFin?: Date;
}

export interface StatistiquesOperations {
  totalOperations: number;
  totalMontantTND: number;
  totalMontantDevise: number;
  repartitionParDevise: { [devise: string]: number };
  repartitionParType: { achat: number; vente: number };
  evolutionMensuelle: { mois: string; montant: number }[];
}
