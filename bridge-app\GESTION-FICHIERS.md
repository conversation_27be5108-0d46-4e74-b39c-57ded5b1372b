# 📁 Gestion des Déclarations Manuelles - Application Bridge

## 🎯 Vue d'ensemble

La nouvelle fonctionnalité de **Gestion des Déclarations Manuelles** permet aux utilisateurs d'importer des fichiers de déclaration (CSV/Excel) pour traitement automatique. Cette fonctionnalité complète le système de déclaration automatisée en offrant une alternative pour les données externes.

## ✨ Fonctionnalités Principales

### 📤 Import de Fichiers
- **Drag & Drop** : Glissez-déposez vos fichiers directement dans la zone prévue
- **Sélection manuelle** : Utilisez le bouton "Parcourir" pour sélectionner vos fichiers
- **Formats supportés** : CSV, Excel (.csv, .xlsx, .xls)
- **Taille maximale** : 10MB par fichier
- **Validation en temps réel** : Vérification du format et de la taille

### 🔍 Traitement Intelligent
- **Validation automatique** des données importées
- **Détection d'erreurs** avec messages explicites
- **Conversion automatique** des formats de date
- **Vérification des devises** et types d'opération
- **Calculs automatiques** des montants et statistiques

### 📊 Rapports Détaillés
- **Statistiques complètes** : nombre de lignes, erreurs, montants
- **Liste des erreurs** avec numéros de ligne
- **Avertissements** pour les formats non standard
- **Résumé visuel** du traitement

### 💾 Gestion des Fichiers
- **Stockage local** des fichiers traités
- **Historique complet** avec dates et statuts
- **Suppression sécurisée** avec confirmation
- **Métadonnées détaillées** pour chaque fichier

## 🏗️ Architecture Technique

### 📡 Services
- **FileProcessingService** : Traitement et validation des fichiers
- **Intégration** avec OperationService pour la cohérence des données
- **Gestion d'état** : Loading, erreurs, succès

### 🎨 Interface Utilisateur
- **Zone de drag & drop** interactive avec animations
- **Indicateurs visuels** pour les différents états
- **Messages contextuels** pour guider l'utilisateur
- **Design responsive** adapté à tous les écrans

### 🔧 Validation des Données
- **Format des colonnes** : Vérification de l'ordre et des noms
- **Types de données** : Validation des montants, dates, devises
- **Règles métier** : Contrôle des CIN, noms clients, etc.
- **Cohérence** : Vérification des calculs et taux

## 📋 Format de Fichier Requis

### 📊 Structure CSV
Le fichier doit contenir les colonnes suivantes **dans l'ordre exact** :

```csv
Date,Type,Devise,Montant Devise,Taux Change,Montant TND,Client,CIN,Numéro Opération
```

### 📝 Description des Colonnes

| Colonne | Type | Format | Exemple | Obligatoire |
|---------|------|--------|---------|-------------|
| **Date** | Date | DD/MM/YYYY, DD-MM-YYYY, YYYY-MM-DD | 15/01/2024 | ✅ |
| **Type** | Texte | ACHAT ou VENTE | ACHAT | ✅ |
| **Devise** | Texte | EUR, USD, GBP, CHF | EUR | ✅ |
| **Montant Devise** | Nombre | Décimal positif | 1000.50 | ✅ |
| **Taux Change** | Nombre | Décimal positif | 3.25 | ✅ |
| **Montant TND** | Nombre | Décimal positif | 3251.25 | ✅ |
| **Client** | Texte | Nom complet | Ahmed Ben Ali | ✅ |
| **CIN** | Texte | 8 chiffres | 12345678 | ✅ |
| **Numéro Opération** | Texte | Identifiant unique | OP-2024-001 | ✅ |

### ⚠️ Règles de Validation

1. **Date** : Doit être une date valide dans l'un des formats supportés
2. **Type** : Uniquement "ACHAT" ou "VENTE" (insensible à la casse)
3. **Devise** : Uniquement EUR, USD, GBP, CHF
4. **Montants** : Doivent être des nombres positifs
5. **CIN** : Exactement 8 chiffres
6. **Client** : Au minimum 2 caractères
7. **Cohérence** : Le montant TND doit correspondre au calcul (Montant Devise × Taux)

## 🚀 Guide d'Utilisation

### 1️⃣ Préparation du Fichier
1. **Créez votre fichier CSV** avec les colonnes requises
2. **Vérifiez les données** selon les règles de validation
3. **Sauvegardez** au format CSV ou Excel

### 2️⃣ Import dans l'Application
1. **Accédez** à l'onglet "Déclarations Manuelles"
2. **Sélectionnez** le bureau de change approprié
3. **Glissez-déposez** votre fichier ou utilisez "Parcourir"
4. **Cliquez** sur "Traiter le fichier"

### 3️⃣ Vérification des Résultats
1. **Consultez** le rapport de traitement
2. **Vérifiez** les statistiques (lignes valides/erreurs)
3. **Corrigez** les erreurs si nécessaire
4. **Réimportez** le fichier corrigé si besoin

### 4️⃣ Gestion des Fichiers
1. **Consultez** l'historique des fichiers traités
2. **Supprimez** les anciens fichiers si nécessaire
3. **Gardez** une trace des déclarations importées

## 📁 Exemple de Fichier

Un fichier d'exemple `exemple-declaration.csv` est fourni avec l'application. Il contient 10 opérations d'exemple avec le format correct.

## 🔧 Gestion d'Erreurs

### ❌ Erreurs Communes
- **Format de date invalide** : Utilisez DD/MM/YYYY
- **Devise non supportée** : Vérifiez EUR, USD, GBP, CHF
- **CIN invalide** : Exactement 8 chiffres requis
- **Montant négatif** : Tous les montants doivent être positifs
- **Colonnes manquantes** : Vérifiez l'ordre et les noms des colonnes

### ✅ Bonnes Pratiques
- **Testez** avec un petit fichier d'abord
- **Vérifiez** les calculs avant import
- **Gardez** une copie de sauvegarde
- **Documentez** vos imports pour audit

## 🔮 Évolutions Futures

- **Support d'autres formats** (JSON, XML)
- **Validation avancée** avec règles personnalisables
- **Import en lot** de plusieurs fichiers
- **Intégration** avec systèmes externes
- **Historique détaillé** avec audit trail
- **Templates** de fichiers prédéfinis

## 📞 Support

Pour toute question ou problème avec l'import de fichiers :
1. **Vérifiez** ce guide d'utilisation
2. **Consultez** les messages d'erreur détaillés
3. **Testez** avec le fichier d'exemple fourni
4. **Contactez** l'équipe de support si nécessaire

---

**Note** : Cette fonctionnalité utilise un traitement local des fichiers pour des raisons de sécurité. Aucune donnée n'est envoyée vers des serveurs externes pendant le traitement.
