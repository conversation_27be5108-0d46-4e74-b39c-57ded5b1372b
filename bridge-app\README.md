# Bridge - Application de Gestion des Opérations de Change

Application Angular pour l'automatisation de la déclaration des opérations entre les bureaux de change en Euro et Dinars Tunisiens pour la Banque Centrale de Tunisie.

## 🚀 Fonctionnalités

- **Gestion des Opérations de Change** : Visualisation et filtrage des opérations d'achat/vente de devises
- **Filtres Avancés** : Filtrage par mois, année, devise, type d'opération et statut
- **Statistiques en Temps Réel** : Affichage des totaux et métriques importantes
- **Génération de Déclarations** : Création automatique des déclarations mensuelles pour la BCT
- **Export de Données** : Téléchargement des déclarations au format CSV
- **Interface Moderne** : Design responsive et professionnel

## 🛠️ Technologies Utilisées

- **Angular 17** - Framework frontend
- **TypeScript** - Langage de programmation
- **SCSS** - Préprocesseur CSS
- **RxJS** - Programmation réactive

## 📋 Prérequis

- Node.js (version 18 ou supérieure)
- npm (version 9 ou supérieure)
- Angular CLI (version 17)

## 🔧 Installation

1. C<PERSON><PERSON> le repository
```bash
git clone <repository-url>
cd bridge-app
```

2. Installer les dépendances
```bash
npm install
```

3. Lancer l'application en mode développement
```bash
ng serve
```

4. Ouvrir le navigateur à l'adresse `http://localhost:4200/`

## 📊 Structure des Données

### Opération de Change
- **ID** : Identifiant unique
- **Date** : Date de l'opération
- **Type** : ACHAT ou VENTE
- **Devise** : EUR, USD, GBP, CHF
- **Montant Devise** : Montant en devise étrangère
- **Taux de Change** : Taux appliqué
- **Montant TND** : Équivalent en Dinars Tunisiens
- **Client** : Informations du client (nom, CIN, nationalité)
- **Statut** : EN_ATTENTE, VALIDEE, DECLAREE, ANNULEE

### Bureau de Change
- **Informations générales** : Nom, adresse, contact
- **Licence** : Numéro de licence officiel
- **Responsable** : Personne en charge
- **Statut** : ACTIF, INACTIF, SUSPENDU

## 🎯 Utilisation

### Filtrage des Opérations
1. Sélectionner le mois et l'année souhaités
2. Choisir optionnellement une devise, un type ou un statut
3. Les résultats se mettent à jour automatiquement

### Génération de Déclarations
1. Sélectionner la période (mois/année)
2. Cliquer sur "Générer Déclaration BCT" pour créer la déclaration
3. Cliquer sur "Télécharger Déclaration" pour exporter au format CSV

### Visualisation des Statistiques
- **Total des opérations** : Nombre d'opérations pour la période
- **Montant total** : Somme en Dinars Tunisiens
- **Période active** : Mois et année sélectionnés

## 🏗️ Architecture

```
src/
├── app/
│   ├── components/
│   │   └── operations-list/          # Composant principal
│   ├── models/
│   │   └── operation.model.ts        # Interfaces TypeScript
│   ├── services/
│   │   └── operation.service.ts      # Service de données
│   ├── app.component.*               # Composant racine
│   └── app.config.ts                 # Configuration
├── assets/                           # Ressources statiques
└── styles.scss                      # Styles globaux
```

## 🔮 Développements Futurs

- Intégration avec une API backend
- Authentification et autorisation
- Gestion multi-bureaux
- Rapports avancés et graphiques
- Notifications en temps réel
- Validation des données renforcée

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/AmazingFeature`)
3. Commit les changements (`git commit -m 'Add some AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrir une Pull Request

## 📝 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 📞 Contact

Pour toute question ou suggestion, veuillez contacter l'équipe de développement.

---

**Note** : Cette application utilise des données factices à des fins de démonstration. Pour un environnement de production, il faudra intégrer une base de données réelle et des mesures de sécurité appropriées.
