import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Operation, BureauChange, FilterCriteria, DeclarationMensuelle } from '../../models/operation.model';
import { OperationService } from '../../services/operation.service';

@Component({
  selector: 'app-operations-list',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './operations-list.component.html',
  styleUrl: './operations-list.component.scss'
})
export class OperationsListComponent implements OnInit {
  operations: Operation[] = [];
  bureauxChange: BureauChange[] = [];
  filteredOperations: Operation[] = [];

  // Filtres
  selectedMois: number = new Date().getMonth() + 1;
  selectedAnnee: number = new Date().getFullYear();
  selectedDevise: string = '';
  selectedType: string = '';
  selectedStatut: string = '';

  // Options pour les filtres
  moisOptions = [
    { value: 1, label: 'Janvier' },
    { value: 2, label: 'Février' },
    { value: 3, label: 'Mars' },
    { value: 4, label: 'Avril' },
    { value: 5, label: 'Mai' },
    { value: 6, label: 'Juin' },
    { value: 7, label: 'Juillet' },
    { value: 8, label: 'Août' },
    { value: 9, label: 'Septembre' },
    { value: 10, label: 'Octobre' },
    { value: 11, label: 'Novembre' },
    { value: 12, label: 'Décembre' }
  ];

  anneeOptions: number[] = [];
  deviseOptions = ['EUR', 'USD', 'GBP', 'CHF'];
  typeOptions = ['ACHAT', 'VENTE'];
  statutOptions = ['EN_ATTENTE', 'VALIDEE', 'DECLAREE', 'ANNULEE'];

  constructor(private operationService: OperationService) {
    // Générer les options d'années (5 dernières années)
    const currentYear = new Date().getFullYear();
    for (let i = 0; i < 5; i++) {
      this.anneeOptions.push(currentYear - i);
    }
  }

  ngOnInit(): void {
    this.loadBureauxChange();
    this.loadOperations();
  }

  loadBureauxChange(): void {
    this.operationService.getBureauxChange().subscribe(bureaux => {
      this.bureauxChange = bureaux;
    });
  }

  loadOperations(): void {
    const filter: FilterCriteria = {
      mois: this.selectedMois,
      annee: this.selectedAnnee,
      devise: this.selectedDevise || undefined,
      type: this.selectedType as any || undefined,
      statut: this.selectedStatut || undefined
    };

    this.operationService.getOperations(filter).subscribe(operations => {
      this.operations = operations;
      this.filteredOperations = operations;
    });
  }

  onFilterChange(): void {
    this.loadOperations();
  }

  genererDeclaration(): void {
    if (this.bureauxChange.length > 0) {
      const bureauId = this.bureauxChange[0].id;
      this.operationService.genererDeclarationMensuelle(bureauId, this.selectedMois, this.selectedAnnee)
        .subscribe(declaration => {
          console.log('Déclaration générée:', declaration);
          alert(`Déclaration générée pour ${this.getMoisLabel(this.selectedMois)} ${this.selectedAnnee}\nNombre d'opérations: ${declaration.totalOperations}`);
        });
    }
  }

  telechargerDeclaration(): void {
    if (this.bureauxChange.length > 0) {
      const bureauId = this.bureauxChange[0].id;
      this.operationService.genererDeclarationMensuelle(bureauId, this.selectedMois, this.selectedAnnee)
        .subscribe(declaration => {
          this.operationService.exporterDeclaration(declaration);
        });
    }
  }

  getMoisLabel(mois: number): string {
    const moisOption = this.moisOptions.find(m => m.value === mois);
    return moisOption ? moisOption.label : '';
  }

  getTotalMontantTND(): number {
    return this.filteredOperations.reduce((total, op) => total + op.montantTND, 0);
  }

  getTotalOperations(): number {
    return this.filteredOperations.length;
  }
}
