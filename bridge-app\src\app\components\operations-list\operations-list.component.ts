import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Operation, BureauChange, FilterCriteria, DeclarationMensuelle } from '../../models/operation.model';
import { OperationService } from '../../services/operation.service';

@Component({
  selector: 'app-operations-list',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './operations-list.component.html',
  styleUrl: './operations-list.component.scss'
})
export class OperationsListComponent implements OnInit {
  operations: Operation[] = [];
  bureauxChange: BureauChange[] = [];
  filteredOperations: Operation[] = [];

  // États de chargement et messages
  isLoading = false;
  isDeclarationLoading = false;
  successMessage: string = '';
  errorMessage: string = '';

  // Filtres
  selectedMois: number = new Date().getMonth() + 1;
  selectedAnnee: number = new Date().getFullYear();
  selectedDevise: string = '';
  selectedType: string = '';
  selectedStatut: string = '';

  // Statistiques pour la déclaration
  operationsValidees: Operation[] = [];
  totalOperationsValidees: number = 0;

  // Options pour les filtres
  moisOptions = [
    { value: 1, label: 'Janvier' },
    { value: 2, label: 'Février' },
    { value: 3, label: 'Mars' },
    { value: 4, label: 'Avril' },
    { value: 5, label: 'Mai' },
    { value: 6, label: 'Juin' },
    { value: 7, label: 'Juillet' },
    { value: 8, label: 'Août' },
    { value: 9, label: 'Septembre' },
    { value: 10, label: 'Octobre' },
    { value: 11, label: 'Novembre' },
    { value: 12, label: 'Décembre' }
  ];

  anneeOptions: number[] = [];
  deviseOptions = ['EUR', 'USD', 'GBP', 'CHF'];
  typeOptions = ['ACHAT', 'VENTE'];
  statutOptions = ['EN_ATTENTE', 'VALIDEE', 'DECLAREE', 'ANNULEE'];

  constructor(private operationService: OperationService) {
    // Générer les options d'années (5 dernières années)
    const currentYear = new Date().getFullYear();
    for (let i = 0; i < 5; i++) {
      this.anneeOptions.push(currentYear - i);
    }
  }

  ngOnInit(): void {
    this.loadBureauxChange();
    this.loadOperations();
  }

  loadBureauxChange(): void {
    this.operationService.getBureauxChange().subscribe({
      next: (bureaux) => {
        this.bureauxChange = bureaux;
      },
      error: (error) => {
        this.errorMessage = 'Erreur lors du chargement des bureaux de change';
        console.error('Erreur:', error);
      }
    });
  }

  loadOperations(): void {
    this.isLoading = true;
    this.clearMessages();

    const filter: FilterCriteria = {
      mois: this.selectedMois,
      annee: this.selectedAnnee,
      devise: this.selectedDevise || undefined,
      type: this.selectedType as any || undefined,
      statut: this.selectedStatut || undefined
    };

    this.operationService.getOperations(filter).subscribe({
      next: (operations) => {
        this.operations = operations;
        this.filteredOperations = operations;
        this.loadOperationsValidees();
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = 'Erreur lors du chargement des opérations';
        this.isLoading = false;
        console.error('Erreur:', error);
      }
    });
  }

  loadOperationsValidees(): void {
    const filter: FilterCriteria = {
      mois: this.selectedMois,
      annee: this.selectedAnnee,
      statut: 'VALIDEE'
    };

    this.operationService.getOperations(filter).subscribe({
      next: (operations) => {
        this.operationsValidees = operations;
        this.totalOperationsValidees = operations.length;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des opérations validées:', error);
      }
    });
  }

  onFilterChange(): void {
    this.loadOperations();
  }

  // Nouvelle méthode pour déclarer toutes les opérations du mois
  declarerOperationsMensuelle(): void {
    if (this.bureauxChange.length === 0) {
      this.errorMessage = 'Aucun bureau de change disponible';
      return;
    }

    if (this.totalOperationsValidees === 0) {
      this.errorMessage = `Aucune opération validée trouvée pour ${this.getMoisLabel(this.selectedMois)} ${this.selectedAnnee}`;
      return;
    }

    const bureauId = this.bureauxChange[0].id;
    this.isDeclarationLoading = true;
    this.clearMessages();

    this.operationService.declarerOperationsMensuelle(bureauId, this.selectedMois, this.selectedAnnee)
      .subscribe({
        next: (response) => {
          this.isDeclarationLoading = false;
          this.successMessage = response.message;
          // Recharger les opérations pour voir les changements de statut
          this.loadOperations();
        },
        error: (error) => {
          this.isDeclarationLoading = false;
          this.errorMessage = error.error || 'Erreur lors de la déclaration à la BCT';
          console.error('Erreur déclaration:', error);
        }
      });
  }

  genererDeclaration(): void {
    if (this.bureauxChange.length > 0) {
      const bureauId = this.bureauxChange[0].id;
      this.operationService.genererDeclarationMensuelle(bureauId, this.selectedMois, this.selectedAnnee)
        .subscribe({
          next: (declaration) => {
            console.log('Déclaration générée:', declaration);
            this.successMessage = `Déclaration générée pour ${this.getMoisLabel(this.selectedMois)} ${this.selectedAnnee} - ${declaration.totalOperations} opérations`;
          },
          error: (error) => {
            this.errorMessage = 'Erreur lors de la génération de la déclaration';
            console.error('Erreur:', error);
          }
        });
    }
  }

  telechargerDeclaration(): void {
    if (this.bureauxChange.length > 0) {
      const bureauId = this.bureauxChange[0].id;
      this.operationService.genererDeclarationMensuelle(bureauId, this.selectedMois, this.selectedAnnee)
        .subscribe({
          next: (declaration) => {
            this.operationService.exporterDeclaration(declaration);
            this.successMessage = 'Déclaration téléchargée avec succès';
          },
          error: (error) => {
            this.errorMessage = 'Erreur lors du téléchargement';
            console.error('Erreur:', error);
          }
        });
    }
  }

  clearMessages(): void {
    this.successMessage = '';
    this.errorMessage = '';
  }

  getMoisLabel(mois: number): string {
    const moisOption = this.moisOptions.find(m => m.value === mois);
    return moisOption ? moisOption.label : '';
  }

  getTotalMontantTND(): number {
    return this.filteredOperations.reduce((total, op) => total + op.montantTND, 0);
  }

  getTotalOperations(): number {
    return this.filteredOperations.length;
  }
}
