import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { FileProcessingService } from '../../services/file-processing.service';
import { OperationService } from '../../services/operation.service';
import { FichierDeclaration, ResultatImport, BureauChange } from '../../models/operation.model';

@Component({
  selector: 'app-file-upload',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './file-upload.component.html',
  styleUrl: './file-upload.component.scss'
})
export class FileUploadComponent implements OnInit {

  // États de l'interface
  isUploading = false;
  isLoadingFiles = false;
  dragOver = false;

  // Données
  bureauxChange: BureauChange[] = [];
  selectedBureauId = '';
  fichiersStockes: FichierDeclaration[] = [];
  dernierResultat: ResultatImport | null = null;

  // Messages
  successMessage = '';
  errorMessage = '';

  // Fichier sélectionné
  selectedFile: File | null = null;

  constructor(
    private fileProcessingService: FileProcessingService,
    private operationService: OperationService
  ) {}

  ngOnInit(): void {
    this.loadBureauxChange();
    this.loadFichiersStockes();
  }

  loadBureauxChange(): void {
    this.operationService.getBureauxChange().subscribe({
      next: (bureaux) => {
        this.bureauxChange = bureaux;
        if (bureaux.length > 0) {
          this.selectedBureauId = bureaux[0].id;
        }
      },
      error: (error) => {
        this.errorMessage = 'Erreur lors du chargement des bureaux de change';
        console.error('Erreur:', error);
      }
    });
  }

  loadFichiersStockes(): void {
    this.isLoadingFiles = true;
    this.fileProcessingService.getFichiersStockes().subscribe({
      next: (fichiers) => {
        this.fichiersStockes = fichiers.sort((a, b) =>
          new Date(b.dateUpload).getTime() - new Date(a.dateUpload).getTime()
        );
        this.isLoadingFiles = false;
      },
      error: (error) => {
        this.errorMessage = 'Erreur lors du chargement des fichiers';
        this.isLoadingFiles = false;
        console.error('Erreur:', error);
      }
    });
  }

  // Gestion du drag & drop
  onDragOver(event: DragEvent): void {
    event.preventDefault();
    this.dragOver = true;
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    this.dragOver = false;
  }

  onDrop(event: DragEvent): void {
    event.preventDefault();
    this.dragOver = false;

    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      this.handleFileSelection(files[0]);
    }
  }

  // Sélection de fichier via input
  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.handleFileSelection(file);
    }
  }

  // Traitement du fichier sélectionné
  handleFileSelection(file: File): void {
    this.clearMessages();

    // Validation du type de fichier
    if (!this.isValidFileType(file)) {
      this.errorMessage = 'Type de fichier non supporté. Veuillez utiliser un fichier CSV ou Excel (.csv, .xlsx, .xls)';
      return;
    }

    // Validation de la taille
    if (file.size > 10 * 1024 * 1024) { // 10MB max
      this.errorMessage = 'Le fichier est trop volumineux. Taille maximale: 10MB';
      return;
    }

    this.selectedFile = file;
  }

  // Uploader et traiter le fichier
  uploadFile(): void {
    if (!this.selectedFile || !this.selectedBureauId) {
      this.errorMessage = 'Veuillez sélectionner un fichier et un bureau de change';
      return;
    }

    this.isUploading = true;
    this.clearMessages();

    this.fileProcessingService.traiterFichier(this.selectedFile, this.selectedBureauId)
      .subscribe({
        next: (resultat) => {
          this.isUploading = false;
          this.dernierResultat = resultat;

          if (resultat.success) {
            this.successMessage = `Fichier traité avec succès! ${resultat.operationsImportees.length} opérations importées.`;
          } else {
            this.errorMessage = `Erreurs détectées lors du traitement: ${resultat.erreurs.length} erreur(s)`;
          }

          // Recharger la liste des fichiers
          this.loadFichiersStockes();

          // Réinitialiser la sélection
          this.selectedFile = null;
          this.resetFileInput();
        },
        error: (error) => {
          this.isUploading = false;
          this.errorMessage = 'Erreur lors du traitement du fichier';
          console.error('Erreur:', error);
        }
      });
  }

  // Supprimer un fichier
  supprimerFichier(id: string): void {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce fichier ?')) {
      this.fileProcessingService.supprimerFichier(id).subscribe({
        next: (success) => {
          if (success) {
            this.successMessage = 'Fichier supprimé avec succès';
            this.loadFichiersStockes();
          } else {
            this.errorMessage = 'Erreur lors de la suppression du fichier';
          }
        },
        error: (error) => {
          this.errorMessage = 'Erreur lors de la suppression du fichier';
          console.error('Erreur:', error);
        }
      });
    }
  }

  // Validation du type de fichier
  private isValidFileType(file: File): boolean {
    const validTypes = [
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];

    const validExtensions = ['.csv', '.xlsx', '.xls'];

    return validTypes.includes(file.type) ||
           validExtensions.some(ext => file.name.toLowerCase().endsWith(ext));
  }

  // Réinitialiser l'input file
  resetFileInput(): void {
    const fileInput = document.getElementById('fileInput') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  }

  // Effacer les messages
  clearMessages(): void {
    this.successMessage = '';
    this.errorMessage = '';
  }

  // Obtenir la classe CSS pour le statut
  getStatutClass(statut: string): string {
    switch (statut) {
      case 'VALIDE': return 'status-valide';
      case 'TRAITE': return 'status-traite';
      case 'ERREUR': return 'status-erreur';
      case 'EN_ATTENTE': return 'status-attente';
      default: return '';
    }
  }

  // Formater la taille du fichier
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Obtenir le nom du bureau de change
  getBureauNom(id: string): string {
    const bureau = this.bureauxChange.find(b => b.id === id);
    return bureau ? bureau.nom : 'Bureau inconnu';
  }
}
