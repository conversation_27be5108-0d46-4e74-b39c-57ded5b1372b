import { Injectable } from '@angular/core';
import { Observable, of, delay, throwError } from 'rxjs';
import { Operation, BureauChange, DeclarationMensuelle } from '../models/operation.model';

@Injectable({
  providedIn: 'root'
})
export class FakeApiService {

  private readonly API_DELAY = 1500; // Simule un délai d'API réaliste

  constructor() { }

  // Simule un appel API pour récupérer les opérations
  getOperations(filters?: any): Observable<{ data: Operation[], total: number, success: boolean }> {
    const operations = this.generateFakeOperations();
    
    let filteredOps = operations;
    if (filters) {
      if (filters.mois && filters.annee) {
        filteredOps = operations.filter(op => 
          op.date.getMonth() + 1 === filters.mois && op.date.getFullYear() === filters.annee
        );
      }
      if (filters.devise) {
        filteredOps = filteredOps.filter(op => op.devise === filters.devise);
      }
      if (filters.type) {
        filteredOps = filteredOps.filter(op => op.type === filters.type);
      }
      if (filters.statut) {
        filteredOps = filteredOps.filter(op => op.statut === filters.statut);
      }
    }

    return of({
      data: filteredOps,
      total: filteredOps.length,
      success: true
    }).pipe(delay(this.API_DELAY));
  }

  // Simule un appel API pour récupérer les bureaux de change
  getBureauxChange(): Observable<{ data: BureauChange[], success: boolean }> {
    const bureaux = [
      {
        id: 'BC001',
        nom: 'Bureau de Change Central Tunis',
        adresse: 'Avenue Habib Bourguiba, Tunis',
        ville: 'Tunis',
        codePostal: '1000',
        telephone: '+216 71 123 456',
        email: '<EMAIL>',
        numeroLicence: 'BC-2020-001',
        responsable: 'Hedi Mansouri',
        statut: 'ACTIF' as const,
        dateCreation: new Date('2020-01-01')
      },
      {
        id: 'BC002',
        nom: 'Bureau de Change Sousse',
        adresse: 'Avenue Bourguiba, Sousse',
        ville: 'Sousse',
        codePostal: '4000',
        telephone: '+216 73 456 789',
        email: '<EMAIL>',
        numeroLicence: 'BC-2021-002',
        responsable: 'Amina Belhadj',
        statut: 'ACTIF' as const,
        dateCreation: new Date('2021-03-15')
      }
    ];

    return of({
      data: bureaux,
      success: true
    }).pipe(delay(this.API_DELAY));
  }

  // Simule un appel API pour déclarer toutes les opérations d'un mois
  declarerOperationsMensuelle(bureauId: string, mois: number, annee: number): Observable<{ 
    declaration: DeclarationMensuelle, 
    operationsDeclarees: number,
    success: boolean,
    message: string 
  }> {
    
    // Simule parfois une erreur pour tester la gestion d'erreurs
    if (Math.random() < 0.1) { // 10% de chance d'erreur
      return throwError(() => ({
        error: 'Erreur de connexion au serveur BCT',
        code: 'BCT_CONNECTION_ERROR',
        success: false
      })).pipe(delay(this.API_DELAY));
    }

    const operations = this.generateFakeOperations().filter(op => 
      op.bureauChangeId === bureauId &&
      op.date.getMonth() + 1 === mois &&
      op.date.getFullYear() === annee &&
      op.statut === 'VALIDEE'
    );

    // Marquer les opérations comme déclarées
    operations.forEach(op => op.statut = 'DECLAREE');

    const declaration: DeclarationMensuelle = {
      id: `DECL-${bureauId}-${annee}-${mois.toString().padStart(2, '0')}-${Date.now()}`,
      bureauChangeId: bureauId,
      mois,
      annee,
      operations,
      totalAchats: operations.filter(op => op.type === 'ACHAT').reduce((sum, op) => sum + op.montantTND, 0),
      totalVentes: operations.filter(op => op.type === 'VENTE').reduce((sum, op) => sum + op.montantTND, 0),
      totalOperations: operations.length,
      statut: 'SOUMISE',
      dateCreation: new Date(),
      dateSoumission: new Date()
    };

    return of({
      declaration,
      operationsDeclarees: operations.length,
      success: true,
      message: `Déclaration soumise avec succès à la BCT. ${operations.length} opérations déclarées.`
    }).pipe(delay(this.API_DELAY));
  }

  // Simule un appel API pour obtenir le statut d'une déclaration
  getStatutDeclaration(declarationId: string): Observable<{
    statut: 'SOUMISE' | 'EN_COURS' | 'VALIDEE' | 'REJETEE',
    message: string,
    dateTraitement?: Date,
    success: boolean
  }> {
    const statuts = ['SOUMISE', 'EN_COURS', 'VALIDEE', 'REJETEE'] as const;
    const statutAleatoire = statuts[Math.floor(Math.random() * statuts.length)];
    
    const messages = {
      'SOUMISE': 'Déclaration reçue par la BCT, en attente de traitement',
      'EN_COURS': 'Déclaration en cours de vérification par la BCT',
      'VALIDEE': 'Déclaration validée et acceptée par la BCT',
      'REJETEE': 'Déclaration rejetée - Veuillez corriger les erreurs signalées'
    };

    return of({
      statut: statutAleatoire,
      message: messages[statutAleatoire],
      dateTraitement: statutAleatoire !== 'SOUMISE' ? new Date() : undefined,
      success: true
    }).pipe(delay(this.API_DELAY));
  }

  private generateFakeOperations(): Operation[] {
    const operations: Operation[] = [];
    const devises = ['EUR', 'USD', 'GBP', 'CHF'] as const;
    const types = ['ACHAT', 'VENTE'] as const;
    const statuts = ['EN_ATTENTE', 'VALIDEE', 'DECLAREE'] as const;
    
    const noms = [
      'Ahmed Ben Ali', 'Fatma Trabelsi', 'Mohamed Sassi', 'Leila Bouazizi', 'Karim Jemli',
      'Sonia Gharbi', 'Nabil Khelifi', 'Rim Bouslama', 'Youssef Mrad', 'Asma Dridi',
      'Hichem Bouzid', 'Najla Hamdi', 'Tarek Mejri', 'Ines Chakroun', 'Fares Ghanmi',
      'Salma Ouali', 'Mehdi Zouari', 'Dorra Slim', 'Rami Khemiri', 'Lina Baccouche'
    ];

    const tauxChange = {
      'EUR': { min: 3.20, max: 3.35 },
      'USD': { min: 3.05, max: 3.15 },
      'GBP': { min: 3.80, max: 3.95 },
      'CHF': { min: 3.40, max: 3.55 }
    };

    for (let i = 1; i <= 50; i++) {
      const devise = devises[Math.floor(Math.random() * devises.length)];
      const type = types[Math.floor(Math.random() * types.length)];
      const statut = statuts[Math.floor(Math.random() * statuts.length)];
      const nom = noms[Math.floor(Math.random() * noms.length)];
      
      // Générer une date aléatoire dans les 6 derniers mois
      const date = new Date();
      date.setMonth(date.getMonth() - Math.floor(Math.random() * 6));
      date.setDate(Math.floor(Math.random() * 28) + 1);
      
      const montantDevise = Math.floor(Math.random() * 5000) + 100;
      const taux = tauxChange[devise].min + Math.random() * (tauxChange[devise].max - tauxChange[devise].min);
      const montantTND = Math.round(montantDevise * taux * 100) / 100;

      operations.push({
        id: i.toString(),
        date,
        type,
        devise,
        montantDevise,
        tauxChange: Math.round(taux * 10000) / 10000,
        montantTND,
        clientNom: nom,
        clientCIN: Math.floor(Math.random() * 90000000) + 10000000 + '',
        clientNationalite: 'Tunisienne',
        bureauChangeId: Math.random() > 0.7 ? 'BC002' : 'BC001',
        numeroOperation: `OP-${date.getFullYear()}-${i.toString().padStart(3, '0')}`,
        statut,
        createdAt: date,
        updatedAt: date
      });
    }

    return operations.sort((a, b) => b.date.getTime() - a.date.getTime());
  }
}
